import json
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import requests
from config import OPENAI_API_KEY, OPENAI_API_BASE
from database import db

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LifeCoachRAG:
    """Sistema RAG (Retrieval-Augmented Generation) per il Life Coach"""
    
    def __init__(self):
        self.model = "gpt-4o-mini"
        self.max_context_length = 4000
        self.conversation_history_limit = 5
    
    def _make_openai_request(self, messages: List[Dict], temperature: float = 0.7) -> Optional[str]:
        """Effettua una richiesta all'API OpenAI con gestione errori robusta"""
        headers = {
            "Authorization": f"Bearer {OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": 1000
        }
        
        try:
            response = requests.post(
                f"{OPENAI_API_BASE}/chat/completions", 
                headers=headers, 
                json=data, 
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Errore richiesta OpenAI: {e}")
            return f"❌ Errore di connessione al servizio AI. Riprova più tardi."
        except KeyError as e:
            logger.error(f"Errore parsing risposta OpenAI: {e}")
            return f"❌ Errore nel processamento della risposta AI."
        except Exception as e:
            logger.error(f"Errore generico OpenAI: {e}")
            return f"❌ Errore imprevisto. Contatta l'amministratore."
    
    def _build_user_context(self, user_id: int) -> str:
        """Costruisce il contesto completo dell'utente per il RAG"""
        context_parts = []
        
        # Informazioni profilo utente
        user_info = db.get_user_info(user_id)
        if user_info:
            context_parts.append("=== PROFILO UTENTE ===")
            for category, data in user_info.items():
                context_parts.append(f"\n{category.upper()}:")
                for key, value in data.items():
                    context_parts.append(f"- {key}: {value}")
        
        # Obiettivi attivi
        goals = db.get_user_goals(user_id, status='active')
        if goals:
            context_parts.append("\n=== OBIETTIVI ATTIVI ===")
            for goal in goals[:5]:  # Limita a 5 obiettivi più recenti
                context_parts.append(f"- {goal['title']}")
                if goal['description']:
                    context_parts.append(f"  Descrizione: {goal['description']}")
                if goal['target_date']:
                    context_parts.append(f"  Scadenza: {goal['target_date']}")
                context_parts.append(f"  Priorità: {goal['priority']}/5")
        
        # Conversazioni recenti
        conversations = db.get_user_conversations(user_id, limit=self.conversation_history_limit)
        if conversations:
            context_parts.append("\n=== CONVERSAZIONI RECENTI ===")
            for conv in reversed(conversations):  # Ordine cronologico
                context_parts.append(f"Utente: {conv['message']}")
                context_parts.append(f"Coach: {conv['response']}")
                context_parts.append("---")
        
        return "\n".join(context_parts)
    
    def _get_system_prompt(self) -> str:
        """Restituisce il prompt di sistema per il Life Coach"""
        return """Sei un Life Coach AI esperto e empatico. Il tuo ruolo è:

🎯 OBIETTIVI PRINCIPALI:
- Aiutare l'utente a raggiungere i suoi obiettivi personali e professionali
- Fornire supporto motivazionale e consigli pratici
- Mantenere un approccio positivo ma realistico
- Adattare i consigli alla situazione specifica dell'utente

💡 STILE DI COMUNICAZIONE:
- Usa un tono caldo, professionale e incoraggiante
- Fai domande specifiche per comprendere meglio la situazione
- Fornisci consigli actionable e concreti
- Usa emoji appropriati per rendere la conversazione più coinvolgente
- Rispondi sempre in italiano

🔍 APPROCCIO:
- Analizza il contesto dell'utente (profilo, obiettivi, conversazioni precedenti)
- Identifica pattern e opportunità di crescita
- Suggerisci strategie personalizzate
- Celebra i progressi e incoraggia durante le difficoltà

⚠️ LIMITI:
- Non fornire consigli medici o psicologici specialistici
- Se rilevi problemi seri, suggerisci di consultare un professionista
- Mantieni sempre un approccio etico e responsabile"""
    
    def get_personalized_advice(self, user_id: int, question: str) -> str:
        """Genera consigli personalizzati usando RAG"""
        try:
            # Costruisci il contesto dell'utente
            user_context = self._build_user_context(user_id)
            
            # Prepara i messaggi per l'AI
            messages = [
                {"role": "system", "content": self._get_system_prompt()},
                {"role": "user", "content": f"CONTESTO UTENTE:\n{user_context}\n\nDOMANDA: {question}"}
            ]
            
            # Ottieni la risposta dall'AI
            response = self._make_openai_request(messages)
            
            if response and not response.startswith("❌"):
                # Salva la conversazione nel database per future referenze
                db.save_conversation(user_id, question, response, user_context[:500])
                
            return response or "❌ Non sono riuscito a generare una risposta. Riprova."
            
        except Exception as e:
            logger.error(f"Errore in get_personalized_advice: {e}")
            return "❌ Errore interno. Riprova più tardi."
    
    def generate_daily_motivation(self, user_id: int) -> str:
        """Genera un messaggio motivazionale giornaliero personalizzato"""
        try:
            user_context = self._build_user_context(user_id)
            
            messages = [
                {"role": "system", "content": self._get_system_prompt()},
                {"role": "user", "content": f"""
CONTESTO UTENTE:
{user_context}

Genera un messaggio motivazionale personalizzato per iniziare la giornata. 
Il messaggio deve essere:
- Breve (max 200 parole)
- Specifico per gli obiettivi dell'utente
- Incoraggiante e positivo
- Con un'azione concreta da fare oggi
"""}
            ]
            
            response = self._make_openai_request(messages, temperature=0.8)
            return response or "🌟 Buongiorno! Oggi è un nuovo giorno pieno di opportunità. Qual è la prima cosa positiva che farai?"
            
        except Exception as e:
            logger.error(f"Errore in generate_daily_motivation: {e}")
            return "🌟 Buongiorno! Oggi è un nuovo giorno pieno di opportunità!"
    
    def analyze_progress(self, user_id: int) -> str:
        """Analizza i progressi dell'utente e fornisce feedback"""
        try:
            user_context = self._build_user_context(user_id)
            goals = db.get_user_goals(user_id)
            
            if not goals:
                return "📊 Non hai ancora definito degli obiettivi. Inizia con /goal per crearne uno!"
            
            messages = [
                {"role": "system", "content": self._get_system_prompt()},
                {"role": "user", "content": f"""
CONTESTO UTENTE:
{user_context}

Analizza i progressi dell'utente verso i suoi obiettivi e fornisci:
1. Un riassunto dei progressi attuali
2. Aree di miglioramento
3. Suggerimenti specifici per i prossimi passi
4. Celebrazione dei successi raggiunti

Mantieni un tono positivo e costruttivo.
"""}
            ]
            
            response = self._make_openai_request(messages)
            return response or "📊 I tuoi progressi sono importanti! Continua così!"
            
        except Exception as e:
            logger.error(f"Errore in analyze_progress: {e}")
            return "📊 Errore nell'analisi dei progressi. Riprova più tardi."
    
    def suggest_goal_actions(self, user_id: int, goal_id: int) -> str:
        """Suggerisce azioni specifiche per un obiettivo"""
        try:
            # Recupera l'obiettivo specifico
            goals = db.get_user_goals(user_id)
            target_goal = next((g for g in goals if g['id'] == goal_id), None)
            
            if not target_goal:
                return "❌ Obiettivo non trovato."
            
            user_context = self._build_user_context(user_id)
            
            messages = [
                {"role": "system", "content": self._get_system_prompt()},
                {"role": "user", "content": f"""
CONTESTO UTENTE:
{user_context}

OBIETTIVO SPECIFICO:
- Titolo: {target_goal['title']}
- Descrizione: {target_goal.get('description', 'N/A')}
- Categoria: {target_goal.get('category', 'N/A')}
- Scadenza: {target_goal.get('target_date', 'N/A')}
- Priorità: {target_goal['priority']}/5

Fornisci 3-5 azioni concrete e specifiche che l'utente può fare questa settimana per avanzare verso questo obiettivo.
Ogni azione deve essere:
- Specifica e misurabile
- Realizzabile in tempi ragionevoli
- Collegata direttamente all'obiettivo
"""}
            ]
            
            response = self._make_openai_request(messages)
            return response or f"🎯 Continua a lavorare su '{target_goal['title']}' - ogni piccolo passo conta!"
            
        except Exception as e:
            logger.error(f"Errore in suggest_goal_actions: {e}")
            return "❌ Errore nel generare suggerimenti. Riprova più tardi."

# Istanza globale del sistema RAG
rag = LifeCoachRAG()

# Funzioni di compatibilità con il codice esistente
def get_personalized_advice(user_info: str, question: str) -> str:
    """Funzione di compatibilità - deprecata, usa rag.get_personalized_advice"""
    logger.warning("Uso di funzione deprecata get_personalized_advice")
    return f"🤖 {question}\n\nPer un'esperienza migliore, usa i nuovi comandi del bot!"
