#!/usr/bin/env python3
"""
Script di test per verificare la configurazione del TeleCoach Bot
Esegui questo script per verificare che tutto sia configurato correttamente
"""

import os
import sys
from datetime import datetime

def test_imports():
    """Testa che tutte le dipendenze siano installate"""
    print("🔍 Testando import delle dipendenze...")
    
    try:
        import telegram
        print("✅ python-telegram-bot: OK")
    except ImportError as e:
        print(f"❌ python-telegram-bot: {e}")
        return False
    
    try:
        import requests
        print("✅ requests: OK")
    except ImportError as e:
        print(f"❌ requests: {e}")
        return False
    
    try:
        from apscheduler.schedulers.background import BackgroundScheduler
        print("✅ APScheduler: OK")
    except ImportError as e:
        print(f"❌ APScheduler: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv: OK")
    except ImportError as e:
        print(f"❌ python-dotenv: {e}")
        return False
    
    try:
        import pytz
        print("✅ pytz: OK")
    except ImportError as e:
        print(f"❌ pytz: {e}")
        return False
    
    return True

def test_config():
    """Testa la configurazione"""
    print("\n🔧 Testando configurazione...")
    
    # Controlla se esiste il file .env
    if not os.path.exists('.env'):
        print("❌ File .env non trovato!")
        print("💡 Copia .env.example in .env e configura i tuoi valori")
        return False
    
    try:
        from config import validate_config, TELEGRAM_BOT_TOKEN, AUTHORIZED_TELEGRAM_USERS, OPENAI_API_KEY
        
        if not TELEGRAM_BOT_TOKEN:
            print("❌ TELEGRAM_BOT_TOKEN non configurato")
            return False
        else:
            print("✅ TELEGRAM_BOT_TOKEN: Configurato")
        
        if not AUTHORIZED_TELEGRAM_USERS:
            print("❌ AUTHORIZED_TELEGRAM_USERS non configurato")
            return False
        else:
            print(f"✅ AUTHORIZED_TELEGRAM_USERS: {len(AUTHORIZED_TELEGRAM_USERS)} utenti autorizzati")
        
        if not OPENAI_API_KEY:
            print("❌ OPENAI_API_KEY non configurato")
            return False
        else:
            print("✅ OPENAI_API_KEY: Configurato")
        
        # Testa validazione
        validate_config()
        print("✅ Validazione configurazione: OK")
        
    except Exception as e:
        print(f"❌ Errore configurazione: {e}")
        return False
    
    return True

def test_database():
    """Testa il database"""
    print("\n💾 Testando database...")
    
    try:
        from database import db
        
        # Testa connessione
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
        print(f"✅ Database connesso: {len(tables)} tabelle trovate")
        
        # Lista tabelle
        table_names = [table[0] for table in tables]
        expected_tables = ['users', 'user_profiles', 'goals', 'progress_logs', 'scheduled_messages', 'conversations']
        
        for table in expected_tables:
            if table in table_names:
                print(f"✅ Tabella '{table}': OK")
            else:
                print(f"❌ Tabella '{table}': Mancante")
        
    except Exception as e:
        print(f"❌ Errore database: {e}")
        return False
    
    return True

def test_ai_connection():
    """Testa la connessione AI (opzionale)"""
    print("\n🤖 Testando connessione AI...")
    
    try:
        from rag_module import rag
        
        # Test semplice (senza fare richieste reali per non consumare quota)
        print("✅ Modulo RAG: Caricato correttamente")
        print("💡 Per testare la connessione OpenAI, usa il bot con /chat")
        
    except Exception as e:
        print(f"❌ Errore modulo AI: {e}")
        return False
    
    return True

def test_scheduler():
    """Testa lo scheduler"""
    print("\n⏰ Testando scheduler...")
    
    try:
        from scheduler_module import scheduler
        
        print("✅ Scheduler: Caricato correttamente")
        
        # Testa avvio/stop
        if not scheduler.is_running:
            scheduler.start()
            print("✅ Scheduler: Avviato")
        
        if scheduler.is_running:
            print("✅ Scheduler: In esecuzione")
            scheduler.stop()
            print("✅ Scheduler: Fermato correttamente")
        
    except Exception as e:
        print(f"❌ Errore scheduler: {e}")
        return False
    
    return True

def main():
    """Funzione principale di test"""
    print("🎯 TeleCoach Bot - Test di Configurazione")
    print("=" * 50)
    print(f"📅 Data test: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version}")
    print("=" * 50)
    
    tests = [
        ("Import Dipendenze", test_imports),
        ("Configurazione", test_config),
        ("Database", test_database),
        ("Modulo AI", test_ai_connection),
        ("Scheduler", test_scheduler)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Errore durante {test_name}: {e}")
            results.append((test_name, False))
    
    # Riepilogo
    print("\n" + "=" * 50)
    print("📊 RIEPILOGO TEST")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"📈 Risultato: {passed}/{total} test superati")
    
    if passed == total:
        print("🎉 Tutti i test sono passati! Il bot è pronto per l'uso.")
        print("\n🚀 Per avviare il bot:")
        print("   python telecoach_bot.py")
    else:
        print("⚠️  Alcuni test sono falliti. Controlla la configurazione.")
        print("\n📖 Consulta il README.md per le istruzioni di setup.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
