# 🎯 TeleCoach - Life Coach AI Bot per Telegram

Un bot Telegram avanzato che funziona come Life Coach personale, utilizzando AI per fornire consigli personalizzati, gestire obiettivi e schedulare promemoria.

## ✨ Funzionalità Principali

### 🤖 AI Life Coach
- **Conversazioni personalizzate** con GPT-4o-mini
- **Consigli basati sul contesto** utilizzando RAG (Retrieval-Augmented Generation)
- **Motivazione quotidiana** personalizzata
- **Analisi progressi** intelligente

### 🎯 Gestione Obiettivi
- Creazione e tracking obiettivi personali
- Sistema di priorità e scadenze
- Suggerimenti AI per raggiungere gli obiettivi
- Monitoraggio progressi nel tempo

### 👤 Profilo Utente Avanzato
- Salvataggio informazioni categorizzate (salute, lavoro, personale, etc.)
- Storico conversazioni per contesto migliore
- Preferenze personalizzabili

### ⏰ Sistema di Schedulazione
- **Promemoria personalizzati** con data/ora specifica
- **Messaggi motivazionali giornalieri** automatici
- **Riepi<PERSON>hi settimanali** degli obiettivi
- **Promemoria ricorrenti** con sintassi cron

### 📊 Analytics e Monitoraggio
- Statistiche personali dettagliate
- Analisi progressi con AI
- Esportazione dati
- Dashboard stato sistema

## 🚀 Installazione

### Prerequisiti
- Python 3.8 o superiore
- Account Telegram
- Token bot Telegram (da @BotFather)
- Chiave API OpenAI

### 1. Clona il Repository
```bash
git clone <repository-url>
cd teleCoach
```

### 2. Installa le Dipendenze
```bash
pip install -r requirements.txt
```

### 3. Configurazione
1. Copia il file di esempio:
```bash
cp .env.example .env
```

2. Modifica il file `.env` con i tuoi dati:
```env
# Token del bot Telegram (ottienilo da @BotFather)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# ID utenti autorizzati (separati da virgola)
AUTHORIZED_TELEGRAM_USERS=*********,*********

# Chiave API OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# Base URL API OpenAI (opzionale)
OPENAI_API_BASE=https://api.openai.com/v1
```

### 4. Avvio del Bot
```bash
python telecoach_bot.py
```

## 📱 Come Usare il Bot

### Comandi Principali

#### 🎯 Gestione Obiettivi
```
/goals - Menu gestione obiettivi
/goal_add <titolo> - Aggiungi nuovo obiettivo
/goal_list - Lista obiettivi attivi
```

**Esempi:**
- `/goal_add Perdere 5kg entro marzo`
- `/goal_add Leggere 12 libri quest'anno`
- `/goal_add Imparare Python`

#### 👤 Gestione Profilo
```
/profile - Menu gestione profilo
/info_save <categoria> <chiave> <valore> - Salva informazione
/info_get [categoria] - Recupera informazioni
```

**Esempi:**
- `/info_save salute peso 75kg`
- `/info_save lavoro ruolo "Software Developer"`
- `/info_save personale hobby "lettura, sport"`

#### 💬 Conversazione AI
```
/chat <messaggio> - Parla con l'AI
/motivation - Ricevi motivazione personalizzata
/analyze - Analizza i tuoi progressi
```

**Esempi:**
- `/chat Come posso essere più produttivo?`
- `/chat Ho difficoltà a mantenere le abitudini`
- `/chat Consigli per gestire lo stress`

#### ⏰ Schedulazione
```
/remind <data> <ora> <messaggio> - Schedula promemoria
```

**Esempi:**
- `/remind 2025-01-15 09:00 Controllo medico`
- `/remind 2025-02-01 18:30 Chiamare mamma`
- `/remind 2025-03-10 12:00 Pausa pranzo mindfulness`

#### ⚙️ Sistema
```
/status - Stato del sistema
/jobs - Lista promemoria attivi
/help - Guida completa
```

## 🏗️ Architettura del Sistema

### Moduli Principali

#### `telecoach_bot.py`
- **Bot principale** con gestione comandi Telegram
- **Menu interattivi** con bottoni inline
- **Autorizzazione utenti** e gestione sessioni
- **Interfaccia utente** completa

#### `database.py`
- **DatabaseManager** per tutte le operazioni DB
- **Tabelle ottimizzate** per life coaching
- **Gestione transazioni** sicura
- **Query efficienti** con indici

#### `rag_module.py`
- **Sistema RAG** (Retrieval-Augmented Generation)
- **Contesto personalizzato** per ogni utente
- **Integrazione OpenAI** con gestione errori
- **Prompt engineering** ottimizzato

#### `scheduler_module.py`
- **Scheduler avanzato** con APScheduler
- **Persistenza job** in database
- **Gestione timezone** automatica
- **Job ricorrenti** e una tantum

#### `config.py`
- **Gestione configurazione** sicura
- **Validazione parametri** all'avvio
- **Variabili d'ambiente** per sicurezza

### Database Schema

```sql
-- Utenti con informazioni dettagliate
users (user_id, username, first_name, last_name, created_at, last_activity, preferences, is_active)

-- Profili utente categorizzati
user_profiles (id, user_id, category, key, value, created_at, updated_at)

-- Obiettivi con priorità e scadenze
goals (id, user_id, title, description, category, target_date, status, priority, created_at, updated_at)

-- Tracking progressi
progress_logs (id, user_id, goal_id, note, rating, created_at)

-- Messaggi schedulati
scheduled_messages (id, user_id, message, message_type, scheduled_time, status, created_at, sent_at)

-- Conversazioni per RAG
conversations (id, user_id, message, response, context, created_at)
```

## 🔒 Sicurezza

### Misure Implementate
- **Token e API key** in variabili d'ambiente
- **Autorizzazione utenti** con whitelist
- **Validazione input** su tutti i comandi
- **Gestione errori** robusta
- **Logging sicuro** senza dati sensibili

### File da NON Committare
```
.env
*.log
__pycache__/
*.pyc
telecoach.db
```

## 🛠️ Personalizzazione

### Modifica Prompt AI
Modifica il metodo `_get_system_prompt()` in `rag_module.py` per personalizzare il comportamento dell'AI.

### Aggiungere Nuovi Comandi
1. Aggiungi il metodo nel `TeleCoachBot`
2. Registra l'handler in `setup_handlers()`
3. Aggiungi la documentazione in `/help`

### Schedulazione Personalizzata
Usa `scheduler.schedule_recurring_reminder()` per creare promemoria ricorrenti con sintassi cron.

## 📊 Monitoraggio

### Log del Sistema
Il bot genera log dettagliati per:
- Avvio/arresto sistema
- Errori e eccezioni
- Attività utenti
- Operazioni database
- Job scheduler

### Metriche Disponibili
- Numero obiettivi attivi per utente
- Conversazioni totali
- Job schedulati
- Stato componenti sistema

## 🚨 Troubleshooting

### Problemi Comuni

#### Bot non risponde
1. Verifica token Telegram in `.env`
2. Controlla autorizzazioni utente
3. Verifica connessione internet

#### Errori AI
1. Controlla chiave OpenAI in `.env`
2. Verifica quota API OpenAI
3. Controlla formato richieste

#### Scheduler non funziona
1. Verifica timezone in `config.py`
2. Controlla permessi database
3. Verifica formato date/ore

#### Database corrotto
```bash
# Backup e reset database
cp telecoach.db telecoach.db.backup
rm telecoach.db
python -c "from database import db; print('Database reinizializzato')"
```

## 🤝 Contribuire

1. Fork del repository
2. Crea branch feature (`git checkout -b feature/AmazingFeature`)
3. Commit modifiche (`git commit -m 'Add AmazingFeature'`)
4. Push al branch (`git push origin feature/AmazingFeature`)
5. Apri Pull Request

## 📄 Licenza

Questo progetto è sotto licenza MIT. Vedi `LICENSE` per dettagli.

## 🙏 Ringraziamenti

- **OpenAI** per l'API GPT
- **python-telegram-bot** per l'SDK Telegram
- **APScheduler** per la schedulazione
- **SQLite** per il database

## 📞 Supporto

Per supporto e domande:
- Apri un Issue su GitHub
- Contatta via Telegram: @your_username

---

**🎯 TeleCoach - Il tuo Life Coach AI personale sempre con te!**
