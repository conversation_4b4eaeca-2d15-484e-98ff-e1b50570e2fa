import os
from dotenv import load_dotenv

# Carica le variabili d'ambiente dal file .env
load_dotenv()

# Configurazione del progetto - USARE VARIABILI D'AMBIENTE PER SICUREZZA
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
AUTHORIZED_TELEGRAM_USERS = [int(x) for x in os.getenv("AUTHORIZED_TELEGRAM_USERS", "").split(",") if x.strip()]

# Configurazione AI
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_API_BASE = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")

# Configurazione Database
DB_PATH = 'telecoach.db'

# Configurazione Scheduler
TIMEZONE = 'Europe/Rome'

# Validazione configurazione
def validate_config():
    """Valida che tutte le configurazioni necessarie siano presenti"""
    errors = []
    
    if not TELEGRAM_BOT_TOKEN:
        errors.append("TELEGRAM_BOT_TOKEN non configurato")
    
    if not AUTHORIZED_TELEGRAM_USERS:
        errors.append("AUTHORIZED_TELEGRAM_USERS non configurato")
    
    if not OPENAI_API_KEY:
        errors.append("OPENAI_API_KEY non configurato")
    
    if errors:
        raise ValueError(f"Errori di configurazione: {', '.join(errors)}")
    
    return True
