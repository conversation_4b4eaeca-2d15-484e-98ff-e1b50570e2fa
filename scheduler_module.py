import logging
from datetime import datetime, timedelta
from typing import Optional, Callable, Any
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.executors.pool import Thread<PERSON>oolExecutor
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from pytz import timezone
from config import TIMEZON<PERSON>, DB_PATH
from database import db

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TeleCoachScheduler:
    """Scheduler avanzato per il Life Coach Bot"""
    
    def __init__(self):
        # Configurazione jobstore per persistenza
        jobstores = {
            'default': SQLAlchemyJobStore(url=f'sqlite:///{DB_PATH}')
        }
        
        # Configurazione executors
        executors = {
            'default': ThreadPoolExecutor(20),
        }
        
        # Configurazione job defaults
        job_defaults = {
            'coalesce': False,
            'max_instances': 3,
            'misfire_grace_time': 300  # 5 minuti di grazia per job mancati
        }
        
        # Inizializza scheduler
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone=timezone(TIMEZONE)
        )
        
        self.bot_application = None  # Sarà impostato dal bot principale
        self.is_running = False
    
    def start(self):
        """Avvia lo scheduler"""
        try:
            if not self.is_running:
                self.scheduler.start()
                self.is_running = True
                logger.info("Scheduler avviato con successo")
                
                # Schedula job ricorrenti
                self._schedule_recurring_jobs()
        except Exception as e:
            logger.error(f"Errore avvio scheduler: {e}")
    
    def stop(self):
        """Ferma lo scheduler"""
        try:
            if self.is_running:
                self.scheduler.shutdown()
                self.is_running = False
                logger.info("Scheduler fermato")
        except Exception as e:
            logger.error(f"Errore stop scheduler: {e}")
    
    def set_bot_application(self, application):
        """Imposta l'applicazione bot per l'invio messaggi"""
        self.bot_application = application
    
    def _schedule_recurring_jobs(self):
        """Schedula job ricorrenti del sistema"""
        try:
            # Job per controllare messaggi schedulati ogni minuto
            self.scheduler.add_job(
                func=self._check_scheduled_messages,
                trigger=IntervalTrigger(minutes=1),
                id='check_scheduled_messages',
                name='Controllo messaggi schedulati',
                replace_existing=True
            )
            
            # Job per messaggi motivazionali giornalieri (8:00 AM)
            self.scheduler.add_job(
                func=self._send_daily_motivations,
                trigger=CronTrigger(hour=8, minute=0),
                id='daily_motivations',
                name='Messaggi motivazionali giornalieri',
                replace_existing=True
            )
            
            # Job per promemoria settimanali obiettivi (Lunedì 9:00 AM)
            self.scheduler.add_job(
                func=self._send_weekly_goal_reminders,
                trigger=CronTrigger(day_of_week='mon', hour=9, minute=0),
                id='weekly_goal_reminders',
                name='Promemoria settimanali obiettivi',
                replace_existing=True
            )
            
            logger.info("Job ricorrenti schedulati con successo")
            
        except Exception as e:
            logger.error(f"Errore schedulazione job ricorrenti: {e}")
    
    def _check_scheduled_messages(self):
        """Controlla e invia messaggi schedulati"""
        try:
            if not self.bot_application:
                return
            
            pending_messages = db.get_pending_messages()
            
            for message_data in pending_messages:
                try:
                    self.bot_application.bot.send_message(
                        chat_id=message_data['user_id'],
                        text=f"⏰ **Promemoria schedulato**\n\n{message_data['message']}"
                    )
                    
                    # Marca il messaggio come inviato
                    db.mark_message_sent(message_data['id'])
                    logger.info(f"Messaggio schedulato inviato a {message_data['user_id']}")
                    
                except Exception as e:
                    logger.error(f"Errore invio messaggio schedulato: {e}")
                    
        except Exception as e:
            logger.error(f"Errore controllo messaggi schedulati: {e}")
    
    def _send_daily_motivations(self):
        """Invia messaggi motivazionali giornalieri"""
        try:
            if not self.bot_application:
                return
            
            from rag_module import rag
            
            # Recupera tutti gli utenti attivi (implementa questa query nel database)
            # Per ora, usa gli utenti che hanno messaggi schedulati
            active_users = self._get_active_users()
            
            for user_id in active_users:
                try:
                    motivation = rag.generate_daily_motivation(user_id)
                    self.bot_application.bot.send_message(
                        chat_id=user_id,
                        text=f"🌅 **Buongiorno!**\n\n{motivation}"
                    )
                    logger.info(f"Motivazione giornaliera inviata a {user_id}")
                    
                except Exception as e:
                    logger.error(f"Errore invio motivazione giornaliera a {user_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Errore invio motivazioni giornaliere: {e}")
    
    def _send_weekly_goal_reminders(self):
        """Invia promemoria settimanali per gli obiettivi"""
        try:
            if not self.bot_application:
                return
            
            from rag_module import rag
            
            active_users = self._get_active_users()
            
            for user_id in active_users:
                try:
                    goals = db.get_user_goals(user_id)
                    if goals:
                        analysis = rag.analyze_progress(user_id)
                        self.bot_application.bot.send_message(
                            chat_id=user_id,
                            text=f"📊 **Riepilogo settimanale obiettivi**\n\n{analysis}"
                        )
                        logger.info(f"Riepilogo settimanale inviato a {user_id}")
                        
                except Exception as e:
                    logger.error(f"Errore invio riepilogo settimanale a {user_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Errore invio riepiloghi settimanali: {e}")
    
    def _get_active_users(self) -> list:
        """Recupera lista utenti attivi (implementazione semplificata)"""
        try:
            # Implementazione semplificata - recupera utenti con attività recente
            with db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT DISTINCT user_id FROM users 
                    WHERE is_active = 1 
                    AND last_activity > datetime('now', '-7 days')
                ''')
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Errore recupero utenti attivi: {e}")
            return []
    
    def schedule_one_time_message(self, user_id: int, message: str, 
                                 send_time: datetime, message_type: str = 'reminder') -> Optional[int]:
        """Schedula un messaggio una tantum"""
        try:
            # Salva nel database
            message_id = db.schedule_message(user_id, message, send_time.isoformat(), message_type)
            
            if message_id:
                # Schedula il job
                job_id = f"message_{message_id}"
                self.scheduler.add_job(
                    func=self._send_scheduled_message,
                    trigger=DateTrigger(run_date=send_time),
                    args=[user_id, message, message_id],
                    id=job_id,
                    name=f"Messaggio schedulato per {user_id}",
                    replace_existing=True
                )
                
                logger.info(f"Messaggio schedulato per {send_time} (ID: {message_id})")
                return message_id
            
        except Exception as e:
            logger.error(f"Errore schedulazione messaggio: {e}")
            return None
    
    def _send_scheduled_message(self, user_id: int, message: str, message_id: int):
        """Invia un messaggio schedulato specifico"""
        try:
            if self.bot_application:
                self.bot_application.bot.send_message(
                    chat_id=user_id,
                    text=f"⏰ **Promemoria**\n\n{message}"
                )
                
                # Marca come inviato
                db.mark_message_sent(message_id)
                logger.info(f"Messaggio {message_id} inviato a {user_id}")
                
        except Exception as e:
            logger.error(f"Errore invio messaggio schedulato {message_id}: {e}")
    
    def schedule_recurring_reminder(self, user_id: int, message: str, 
                                  cron_expression: str, reminder_name: str) -> bool:
        """Schedula un promemoria ricorrente"""
        try:
            job_id = f"recurring_{user_id}_{reminder_name.replace(' ', '_')}"
            
            # Parsing dell'espressione cron
            cron_parts = cron_expression.split()
            if len(cron_parts) != 5:
                raise ValueError("Espressione cron non valida")
            
            minute, hour, day, month, day_of_week = cron_parts
            
            self.scheduler.add_job(
                func=self._send_recurring_reminder,
                trigger=CronTrigger(
                    minute=minute if minute != '*' else None,
                    hour=hour if hour != '*' else None,
                    day=day if day != '*' else None,
                    month=month if month != '*' else None,
                    day_of_week=day_of_week if day_of_week != '*' else None
                ),
                args=[user_id, message],
                id=job_id,
                name=f"Promemoria ricorrente: {reminder_name}",
                replace_existing=True
            )
            
            logger.info(f"Promemoria ricorrente '{reminder_name}' schedulato per {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Errore schedulazione promemoria ricorrente: {e}")
            return False
    
    def _send_recurring_reminder(self, user_id: int, message: str):
        """Invia un promemoria ricorrente"""
        try:
            if self.bot_application:
                self.bot_application.bot.send_message(
                    chat_id=user_id,
                    text=f"🔔 **Promemoria ricorrente**\n\n{message}"
                )
                logger.info(f"Promemoria ricorrente inviato a {user_id}")
                
        except Exception as e:
            logger.error(f"Errore invio promemoria ricorrente: {e}")
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancella un job schedulato"""
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Job {job_id} cancellato")
            return True
        except Exception as e:
            logger.error(f"Errore cancellazione job {job_id}: {e}")
            return False
    
    def list_jobs(self) -> list:
        """Lista tutti i job attivi"""
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
            return jobs
        except Exception as e:
            logger.error(f"Errore lista job: {e}")
            return []

# Istanza globale dello scheduler
scheduler = TeleCoachScheduler()

# Funzioni di compatibilità
def schedule_custom_action(run_time: datetime, action: Callable, *args, **kwargs):
    """Funzione di compatibilità - deprecata"""
    logger.warning("Uso di funzione deprecata schedule_custom_action")
    pass

def example_action(user_id: int, message: str):
    """Funzione di esempio - deprecata"""
    logger.warning("Uso di funzione deprecata example_action")
    print(f"[SCHEDULATO] Messaggio per {user_id}: {message}")
