# 🚀 Guida al Deployment - TeleCoach Bot

Questa guida ti aiuterà a deployare il TeleCoach Bot in produzione.

## 📋 Checklist Pre-Deployment

### ✅ Configurazione
- [ ] File `.env` configurato con token e API key reali
- [ ] Utenti autorizzati aggiunti in `AUTHORIZED_TELEGRAM_USERS`
- [ ] Chiave OpenAI valida e con credito sufficiente
- [ ] Test di configurazione superati (`python start.py test`)

### ✅ Sicurezza
- [ ] File `.env` NON committato nel repository
- [ ] Token e API key mantenuti segreti
- [ ] Accesso al server limitato
- [ ] Backup del database configurato

### ✅ Ambiente
- [ ] Python 3.8+ installato
- [ ] Dipendenze installate (`pip install -r requirements.txt`)
- [ ] Permessi di scrittura per database
- [ ] Connessione internet stabile

## 🖥️ Deployment su Server Linux

### 1. Preparazione Server
```bash
# Aggiorna sistema
sudo apt update && sudo apt upgrade -y

# Installa Python e pip
sudo apt install python3 python3-pip python3-venv -y

# Crea utente dedicato
sudo useradd -m -s /bin/bash telecoach
sudo su - telecoach
```

### 2. Setup Applicazione
```bash
# Clona repository
git clone <your-repo-url> telecoach-bot
cd telecoach-bot

# Crea ambiente virtuale
python3 -m venv .venv
source .venv/bin/activate

# Installa dipendenze
pip install -r requirements.txt

# Configura ambiente
cp .env.example .env
nano .env  # Configura i tuoi valori
```

### 3. Test Configurazione
```bash
# Testa setup
python start.py test

# Test avvio rapido
timeout 10 python start.py || echo "Test avvio OK"
```

### 4. Servizio Systemd
```bash
# Crea file servizio
sudo nano /etc/systemd/system/telecoach.service
```

Contenuto del file servizio:
```ini
[Unit]
Description=TeleCoach Bot - Life Coach AI
After=network.target
Wants=network.target

[Service]
Type=simple
User=telecoach
Group=telecoach
WorkingDirectory=/home/<USER>/telecoach-bot
Environment=PATH=/home/<USER>/telecoach-bot/.venv/bin
ExecStart=/home/<USER>/telecoach-bot/.venv/bin/python start.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 5. Avvio Servizio
```bash
# Ricarica systemd
sudo systemctl daemon-reload

# Abilita servizio
sudo systemctl enable telecoach

# Avvia servizio
sudo systemctl start telecoach

# Controlla stato
sudo systemctl status telecoach

# Visualizza log
sudo journalctl -u telecoach -f
```

## 🐳 Deployment con Docker

### 1. Dockerfile
```dockerfile
FROM python:3.11-slim

# Installa dipendenze sistema
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Crea utente non-root
RUN useradd -m -u 1000 telecoach

# Directory di lavoro
WORKDIR /app

# Copia requirements e installa dipendenze
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copia codice applicazione
COPY . .

# Cambia proprietario
RUN chown -R telecoach:telecoach /app

# Usa utente non-root
USER telecoach

# Esponi porta (se necessario per webhook)
EXPOSE 8080

# Comando di avvio
CMD ["python", "start.py"]
```

### 2. Docker Compose
```yaml
version: '3.8'

services:
  telecoach:
    build: .
    container_name: telecoach-bot
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - AUTHORIZED_TELEGRAM_USERS=${AUTHORIZED_TELEGRAM_USERS}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_BASE=${OPENAI_API_BASE}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    env_file:
      - .env
```

### 3. Comandi Docker
```bash
# Build immagine
docker build -t telecoach-bot .

# Avvio con docker-compose
docker-compose up -d

# Visualizza log
docker-compose logs -f

# Stop
docker-compose down
```

## ☁️ Deployment su Cloud

### Heroku
```bash
# Installa Heroku CLI
# Crea Procfile
echo "worker: python start.py" > Procfile

# Deploy
heroku create your-telecoach-bot
heroku config:set TELEGRAM_BOT_TOKEN=your_token
heroku config:set AUTHORIZED_TELEGRAM_USERS=your_user_ids
heroku config:set OPENAI_API_KEY=your_openai_key
git push heroku main
```

### Railway
```bash
# Installa Railway CLI
npm install -g @railway/cli

# Deploy
railway login
railway init
railway up
```

### DigitalOcean App Platform
1. Connetti repository GitHub
2. Configura variabili d'ambiente
3. Deploy automatico

## 📊 Monitoraggio

### Log Management
```bash
# Rotazione log con logrotate
sudo nano /etc/logrotate.d/telecoach

# Contenuto:
/home/<USER>/telecoach-bot/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 telecoach telecoach
}
```

### Health Check
```bash
# Script di health check
#!/bin/bash
if systemctl is-active --quiet telecoach; then
    echo "TeleCoach Bot: RUNNING"
    exit 0
else
    echo "TeleCoach Bot: STOPPED"
    exit 1
fi
```

### Backup Database
```bash
# Script backup giornaliero
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
cp /home/<USER>/telecoach-bot/telecoach.db \
   /home/<USER>/backups/telecoach_$DATE.db

# Mantieni solo ultimi 7 backup
find /home/<USER>/backups -name "telecoach_*.db" -mtime +7 -delete
```

## 🔧 Manutenzione

### Aggiornamenti
```bash
# Backup prima dell'aggiornamento
cp telecoach.db telecoach.db.backup

# Pull nuove modifiche
git pull origin main

# Aggiorna dipendenze se necessario
pip install -r requirements.txt

# Riavvia servizio
sudo systemctl restart telecoach
```

### Troubleshooting
```bash
# Controlla log
sudo journalctl -u telecoach -n 50

# Controlla stato
sudo systemctl status telecoach

# Test configurazione
python start.py test

# Riavvio forzato
sudo systemctl stop telecoach
sudo systemctl start telecoach
```

## 📈 Scaling

### Multiple Instances
Per gestire più utenti, puoi deployare multiple istanze:

1. **Load Balancer**: Nginx per distribuire richieste
2. **Database Condiviso**: PostgreSQL invece di SQLite
3. **Redis**: Per cache e sessioni condivise
4. **Queue System**: Celery per job asincroni

### Performance Optimization
- Usa connection pooling per database
- Implementa cache per risposte AI frequenti
- Ottimizza query database
- Monitora uso memoria e CPU

## 🚨 Sicurezza in Produzione

### Firewall
```bash
# Configura UFW
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow from trusted_ip to any port 22
```

### SSL/TLS
Se usi webhook invece di polling:
```bash
# Certbot per Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

### Monitoring
- Configura alerting per downtime
- Monitora uso risorse
- Log di sicurezza
- Backup automatici

---

**🎯 Il tuo TeleCoach Bot è ora pronto per la produzione!**
