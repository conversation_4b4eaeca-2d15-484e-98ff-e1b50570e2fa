#!/usr/bin/env python3
"""
Script di avvio rapido per TeleCoach Bot
Questo script gestisce l'avvio del bot con controlli preliminari
"""

import os
import sys
import asyncio
import signal
from datetime import datetime

def print_banner():
    """Stampa il banner di avvio"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎯 TELECOACH BOT                          ║
    ║                Life Coach AI per Telegram                    ║
    ║                                                              ║
    ║  🤖 AI-Powered  📊 Analytics  ⏰ Scheduling  🎯 Goal Tracking ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)
    print(f"    📅 Avvio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"    🐍 Python: {sys.version.split()[0]}")
    print("    " + "="*62)

def check_requirements():
    """Controlla i requisiti minimi"""
    print("🔍 Controllo requisiti...")
    
    # Controlla versione Python
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ richiesto")
        return False
    
    # Controlla file .env
    if not os.path.exists('.env'):
        print("❌ File .env mancante!")
        print("💡 Esegui: cp .env.example .env")
        print("💡 Poi configura i tuoi token e API key")
        return False
    
    # Controlla dipendenze critiche
    try:
        import telegram
        import requests
        from apscheduler.schedulers.background import BackgroundScheduler
        from dotenv import load_dotenv
        import pytz
    except ImportError as e:
        print(f"❌ Dipendenza mancante: {e}")
        print("💡 Esegui: pip install -r requirements.txt")
        return False
    
    print("✅ Requisiti soddisfatti")
    return True

def setup_signal_handlers():
    """Configura gestori per segnali di sistema"""
    def signal_handler(signum, frame):
        print(f"\n🛑 Ricevuto segnale {signum}")
        print("🔄 Arresto graceful del bot...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """Funzione principale"""
    print_banner()
    
    # Controlli preliminari
    if not check_requirements():
        print("\n❌ Controlli preliminari falliti!")
        print("📖 Consulta README.md per le istruzioni")
        return False
    
    # Setup signal handlers
    setup_signal_handlers()
    
    print("🚀 Avvio TeleCoach Bot...")
    print("💡 Premi Ctrl+C per fermare il bot")
    print("="*62)
    
    try:
        # Import e avvio bot
        from telecoach_bot import main as bot_main
        await bot_main()
        
    except KeyboardInterrupt:
        print("\n🛑 Bot fermato dall'utente")
        return True
    except Exception as e:
        print(f"\n❌ Errore critico: {e}")
        print("📖 Controlla i log per maggiori dettagli")
        return False

def run_tests():
    """Esegue i test di configurazione"""
    print("🧪 Esecuzione test di configurazione...")
    try:
        from test_setup import main as test_main
        return test_main()
    except Exception as e:
        print(f"❌ Errore durante i test: {e}")
        return False

if __name__ == "__main__":
    # Controlla argomenti
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            # Modalità test
            success = run_tests()
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "help":
            # Mostra aiuto
            print("🎯 TeleCoach Bot - Script di Avvio")
            print("\nComandi disponibili:")
            print("  python start.py        - Avvia il bot")
            print("  python start.py test   - Esegui test configurazione")
            print("  python start.py help   - Mostra questo aiuto")
            print("\nPer maggiori informazioni consulta README.md")
            sys.exit(0)
        else:
            print(f"❌ Comando sconosciuto: {sys.argv[1]}")
            print("💡 Usa 'python start.py help' per vedere i comandi disponibili")
            sys.exit(1)
    
    # Avvio normale
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Errore fatale: {e}")
        sys.exit(1)
