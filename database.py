import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional, Any
from config import DB_PATH
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Gestisce tutte le operazioni del database per il Life Coach Bot"""
    
    def __init__(self, db_path: str = DB_PATH):
        self.db_path = db_path
        self.init_db()
    
    def get_connection(self):
        """Crea una connessione al database con gestione errori"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Per accesso ai campi per nome
            return conn
        except sqlite3.Error as e:
            logger.error(f"Errore connessione database: {e}")
            raise
    
    def init_db(self):
        """Inizializza il database con tutte le tabelle necessarie"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Tabella utenti con informazioni dettagliate
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        user_id INTEGER PRIMARY KEY,
                        username TEXT,
                        first_name TEXT,
                        last_name TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        preferences TEXT DEFAULT '{}',
                        is_active BOOLEAN DEFAULT 1
                    )
                ''')
                
                # Tabella per le informazioni personali dell'utente
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_profiles (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        category TEXT NOT NULL,
                        key TEXT NOT NULL,
                        value TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (user_id),
                        UNIQUE(user_id, category, key)
                    )
                ''')
                
                # Tabella per gli obiettivi
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS goals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        title TEXT NOT NULL,
                        description TEXT,
                        category TEXT,
                        target_date DATE,
                        status TEXT DEFAULT 'active',
                        priority INTEGER DEFAULT 3,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (user_id)
                    )
                ''')
                
                # Tabella per il tracking dei progressi
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS progress_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        goal_id INTEGER,
                        note TEXT,
                        rating INTEGER CHECK(rating >= 1 AND rating <= 10),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (user_id),
                        FOREIGN KEY (goal_id) REFERENCES goals (id)
                    )
                ''')
                
                # Tabella messaggi schedulati migliorata
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scheduled_messages (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        message TEXT NOT NULL,
                        message_type TEXT DEFAULT 'reminder',
                        scheduled_time TIMESTAMP NOT NULL,
                        status TEXT DEFAULT 'pending',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        sent_at TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (user_id)
                    )
                ''')
                
                # Tabella per le conversazioni (per RAG)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS conversations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        message TEXT NOT NULL,
                        response TEXT,
                        context TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (user_id)
                    )
                ''')
                
                conn.commit()
                logger.info("Database inizializzato con successo")
                
        except sqlite3.Error as e:
            logger.error(f"Errore inizializzazione database: {e}")
            raise
    
    # --- GESTIONE UTENTI ---
    
    def create_or_update_user(self, user_id: int, username: str = None, 
                             first_name: str = None, last_name: str = None) -> bool:
        """Crea o aggiorna un utente"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO users 
                    (user_id, username, first_name, last_name, last_activity)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (user_id, username, first_name, last_name))
                conn.commit()
                return True
        except sqlite3.Error as e:
            logger.error(f"Errore creazione/aggiornamento utente: {e}")
            return False
    
    def get_user(self, user_id: int) -> Optional[Dict]:
        """Recupera informazioni utente"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM users WHERE user_id = ?', (user_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
        except sqlite3.Error as e:
            logger.error(f"Errore recupero utente: {e}")
            return None
    
    # --- GESTIONE PROFILO UTENTE ---
    
    def save_user_info(self, user_id: int, category: str, key: str, value: str) -> bool:
        """Salva informazioni specifiche dell'utente"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO user_profiles 
                    (user_id, category, key, value, updated_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (user_id, category, key, value))
                conn.commit()
                return True
        except sqlite3.Error as e:
            logger.error(f"Errore salvataggio info utente: {e}")
            return False
    
    def get_user_info(self, user_id: int, category: str = None) -> Dict[str, Any]:
        """Recupera informazioni utente per categoria"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if category:
                    cursor.execute('''
                        SELECT key, value FROM user_profiles 
                        WHERE user_id = ? AND category = ?
                    ''', (user_id, category))
                else:
                    cursor.execute('''
                        SELECT category, key, value FROM user_profiles 
                        WHERE user_id = ?
                    ''', (user_id,))
                
                rows = cursor.fetchall()
                if category:
                    return {row['key']: row['value'] for row in rows}
                else:
                    result = {}
                    for row in rows:
                        if row['category'] not in result:
                            result[row['category']] = {}
                        result[row['category']][row['key']] = row['value']
                    return result
        except sqlite3.Error as e:
            logger.error(f"Errore recupero info utente: {e}")
            return {}
    
    # --- GESTIONE OBIETTIVI ---
    
    def create_goal(self, user_id: int, title: str, description: str = None, 
                   category: str = None, target_date: str = None, priority: int = 3) -> Optional[int]:
        """Crea un nuovo obiettivo"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO goals (user_id, title, description, category, target_date, priority)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_id, title, description, category, target_date, priority))
                conn.commit()
                return cursor.lastrowid
        except sqlite3.Error as e:
            logger.error(f"Errore creazione obiettivo: {e}")
            return None
    
    def get_user_goals(self, user_id: int, status: str = 'active') -> List[Dict]:
        """Recupera obiettivi utente"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM goals 
                    WHERE user_id = ? AND status = ?
                    ORDER BY priority DESC, created_at DESC
                ''', (user_id, status))
                return [dict(row) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            logger.error(f"Errore recupero obiettivi: {e}")
            return []
    
    # --- GESTIONE MESSAGGI SCHEDULATI ---
    
    def schedule_message(self, user_id: int, message: str, scheduled_time: str, 
                        message_type: str = 'reminder') -> Optional[int]:
        """Schedula un messaggio"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO scheduled_messages 
                    (user_id, message, message_type, scheduled_time)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, message, message_type, scheduled_time))
                conn.commit()
                return cursor.lastrowid
        except sqlite3.Error as e:
            logger.error(f"Errore schedulazione messaggio: {e}")
            return None
    
    def get_pending_messages(self) -> List[Dict]:
        """Recupera messaggi in attesa di invio"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM scheduled_messages 
                    WHERE status = 'pending' AND scheduled_time <= CURRENT_TIMESTAMP
                    ORDER BY scheduled_time ASC
                ''')
                return [dict(row) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            logger.error(f"Errore recupero messaggi pending: {e}")
            return []
    
    def mark_message_sent(self, message_id: int) -> bool:
        """Marca un messaggio come inviato"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE scheduled_messages 
                    SET status = 'sent', sent_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (message_id,))
                conn.commit()
                return True
        except sqlite3.Error as e:
            logger.error(f"Errore aggiornamento messaggio: {e}")
            return False
    
    # --- GESTIONE CONVERSAZIONI ---
    
    def save_conversation(self, user_id: int, message: str, response: str, context: str = None) -> bool:
        """Salva una conversazione per il RAG"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO conversations (user_id, message, response, context)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, message, response, context))
                conn.commit()
                return True
        except sqlite3.Error as e:
            logger.error(f"Errore salvataggio conversazione: {e}")
            return False
    
    def get_user_conversations(self, user_id: int, limit: int = 10) -> List[Dict]:
        """Recupera conversazioni recenti per il contesto"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM conversations 
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (user_id, limit))
                return [dict(row) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            logger.error(f"Errore recupero conversazioni: {e}")
            return []

# Istanza globale del database manager
db = DatabaseManager()
