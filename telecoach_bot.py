import logging
from datetime import datetime, timedelta
from typing import Dict, List
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
from telegram.constants import ParseMode

# Import moduli personalizzati
from config import validate_config, TELEGRAM_BOT_TOKEN, AUTHORIZED_TELEGRAM_USERS
from database import db
from rag_module import rag
from scheduler_module import scheduler

# Configurazione logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TeleCoachBot:
    """Bot Telegram Life Coach con funzionalità avanzate"""
    
    def __init__(self):
        self.application = None
        self.user_states: Dict[int, str] = {}  # Traccia lo stato delle conversazioni
    
    def authorized_only(func):
        """Decorator per autorizzazione utenti"""
        async def wrapper(self, update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
            user_id = update.effective_user.id
            if user_id not in AUTHORIZED_TELEGRAM_USERS:
                await update.message.reply_text(
                    '❌ Non sei autorizzato a usare questo bot.\n'
                    'Contatta l\'amministratore per ottenere l\'accesso.'
                )
                return
            
            # Aggiorna informazioni utente nel database
            user = update.effective_user
            db.create_or_update_user(
                user_id=user_id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name
            )
            
            return await func(self, update, context, *args, **kwargs)
        return wrapper
    
    # --- COMANDI PRINCIPALI ---
    
    @authorized_only
    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Comando /start - Benvenuto e menu principale"""
        user_name = update.effective_user.first_name or "Utente"
        
        welcome_message = f"""
🎯 **Benvenuto nel tuo Life Coach AI, {user_name}!**

Sono qui per aiutarti a raggiungere i tuoi obiettivi e migliorare la tua vita quotidiana.

**🚀 Cosa posso fare per te:**
• 💬 Conversazioni personalizzate e consigli
• 🎯 Gestione obiettivi e tracking progressi
• ⏰ Promemoria e messaggi schedulati
• 📊 Analisi dei tuoi progressi
• 🌟 Motivazione quotidiana personalizzata

**📋 Comandi principali:**
/help - Mostra tutti i comandi disponibili
/profile - Gestisci il tuo profilo
/goals - Gestisci i tuoi obiettivi
/remind - Schedula promemoria
/chat - Inizia una conversazione con l'AI

Inizia con /help per vedere tutti i comandi disponibili!
        """
        
        keyboard = [
            [InlineKeyboardButton("📋 Aiuto", callback_data="help")],
            [InlineKeyboardButton("👤 Profilo", callback_data="profile"),
             InlineKeyboardButton("🎯 Obiettivi", callback_data="goals")],
            [InlineKeyboardButton("📊 Progressi", callback_data="progress"),
             InlineKeyboardButton("💬 Chat AI", callback_data="chat")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            welcome_message, 
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    
    @authorized_only
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Comando /help - Lista completa comandi"""
        help_text = """
📚 **GUIDA COMPLETA LIFE COACH BOT**

**🎯 GESTIONE OBIETTIVI**
/goals - Menu gestione obiettivi
/goal_add <titolo> - Aggiungi nuovo obiettivo
/goal_list - Lista obiettivi attivi

**👤 PROFILO UTENTE**
/profile - Menu gestione profilo
/info_save <categoria> <chiave> <valore> - Salva informazione
/info_get [categoria] - Recupera informazioni

**⏰ SCHEDULAZIONE**
/remind <data> <ora> <messaggio> - Schedula promemoria

**💬 CONVERSAZIONE AI**
/chat <messaggio> - Parla con l'AI
/analyze - Analizza i tuoi progressi
/motivation - Ricevi motivazione personalizzata

**⚙️ SISTEMA**
/status - Stato del sistema
/jobs - Lista promemoria attivi

**💡 ESEMPI D'USO:**
• `/goal_add Perdere 5kg entro marzo`
• `/info_save salute peso 75kg`
• `/remind 2025-01-15 09:00 Controllo medico`
• `/chat Come posso migliorare la mia produttività?`

Usa i menu interattivi per un'esperienza più semplice!
        """
        
        await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)
    
    # --- GESTIONE PROFILO ---
    
    @authorized_only
    async def save_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Salva informazioni utente - /info_save categoria chiave valore"""
        if len(context.args) < 3:
            await update.message.reply_text(
                "❌ **Formato non corretto**\n\n"
                "Usa: `/info_save <categoria> <chiave> <valore>`\n\n"
                "**Esempi:**\n"
                "• `/info_save salute peso 75kg`\n"
                "• `/info_save lavoro ruolo \"Software Developer\"`\n"
                "• `/info_save personale hobby \"lettura, sport\"`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        user_id = update.effective_user.id
        category = context.args[0].lower()
        key = context.args[1].lower()
        value = ' '.join(context.args[2:])
        
        if db.save_user_info(user_id, category, key, value):
            await update.message.reply_text(
                f"✅ **Informazione salvata!**\n\n"
                f"📂 Categoria: {category.title()}\n"
                f"🔑 Chiave: {key.title()}\n"
                f"💾 Valore: {value}",
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text("❌ Errore nel salvataggio. Riprova.")
    
    @authorized_only
    async def get_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Recupera informazioni utente - /info_get [categoria]"""
        user_id = update.effective_user.id
        category = context.args[0].lower() if context.args else None
        
        user_info = db.get_user_info(user_id, category)
        
        if not user_info:
            await update.message.reply_text(
                "📭 **Nessuna informazione trovata**\n\n"
                "Usa `/info_save` per aggiungere informazioni al tuo profilo.",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        if category:
            # Mostra informazioni di una categoria specifica
            info_text = f"📂 **{category.title()}**\n\n"
            for key, value in user_info.items():
                info_text += f"• **{key.title()}:** {value}\n"
        else:
            # Mostra tutte le informazioni
            info_text = "👤 **IL TUO PROFILO**\n\n"
            for cat, data in user_info.items():
                info_text += f"📂 **{cat.title()}**\n"
                for key, value in data.items():
                    info_text += f"  • {key.title()}: {value}\n"
                info_text += "\n"
        
        await update.message.reply_text(info_text, parse_mode=ParseMode.MARKDOWN)
    
    # --- GESTIONE OBIETTIVI ---
    
    @authorized_only
    async def add_goal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Aggiungi nuovo obiettivo - /goal_add titolo"""
        if not context.args:
            await update.message.reply_text(
                "❌ **Formato non corretto**\n\n"
                "Usa: `/goal_add <titolo obiettivo>`\n\n"
                "**Esempi:**\n"
                "• `/goal_add Perdere 5kg entro marzo`\n"
                "• `/goal_add Leggere 12 libri quest'anno`\n"
                "• `/goal_add Imparare Python`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        user_id = update.effective_user.id
        title = ' '.join(context.args)
        
        goal_id = db.create_goal(user_id, title)
        
        if goal_id:
            await update.message.reply_text(
                f"✅ **Obiettivo creato!**\n\n"
                f"🎯 **{title}**\n"
                f"🆔 ID: {goal_id}",
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text("❌ Errore nella creazione dell'obiettivo.")
    
    @authorized_only
    async def list_goals(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Lista obiettivi attivi"""
        user_id = update.effective_user.id
        goals = db.get_user_goals(user_id)
        
        if not goals:
            await update.message.reply_text(
                "📭 **Nessun obiettivo trovato**\n\n"
                "Crea il tuo primo obiettivo con `/goal_add`!",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        goals_text = "🎯 **I TUOI OBIETTIVI ATTIVI**\n\n"
        
        for goal in goals:
            priority_stars = "⭐" * goal['priority']
            goals_text += f"**{goal['id']}.** {goal['title']}\n"
            if goal['description']:
                goals_text += f"   📝 {goal['description']}\n"
            if goal['target_date']:
                goals_text += f"   📅 Scadenza: {goal['target_date']}\n"
            goals_text += f"   {priority_stars} Priorità: {goal['priority']}/5\n\n"
        
        await update.message.reply_text(goals_text, parse_mode=ParseMode.MARKDOWN)
    
    # --- CONVERSAZIONE AI ---
    
    @authorized_only
    async def chat_ai(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Conversazione con AI - /chat messaggio"""
        if not context.args:
            await update.message.reply_text(
                "💬 **Chat con il Life Coach AI**\n\n"
                "Usa: `/chat <la tua domanda o messaggio>`\n\n"
                "**Esempi:**\n"
                "• `/chat Come posso essere più produttivo?`\n"
                "• `/chat Ho difficoltà a mantenere le abitudini`\n"
                "• `/chat Consigli per gestire lo stress`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        user_id = update.effective_user.id
        question = ' '.join(context.args)
        
        # Mostra indicatore di digitazione
        await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")
        
        # Ottieni risposta personalizzata
        response = rag.get_personalized_advice(user_id, question)
        
        await update.message.reply_text(
            f"🤖 **Life Coach AI**\n\n{response}",
            parse_mode=ParseMode.MARKDOWN
        )
    
    @authorized_only
    async def get_motivation(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Ricevi motivazione personalizzata"""
        user_id = update.effective_user.id
        
        await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")
        
        motivation = rag.generate_daily_motivation(user_id)
        
        await update.message.reply_text(
            f"🌟 **MOTIVAZIONE PERSONALIZZATA**\n\n{motivation}",
            parse_mode=ParseMode.MARKDOWN
        )
    
    @authorized_only
    async def analyze_progress(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Analizza progressi utente"""
        user_id = update.effective_user.id
        
        await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")
        
        analysis = rag.analyze_progress(user_id)
        
        await update.message.reply_text(
            f"📊 **ANALISI PROGRESSI**\n\n{analysis}",
            parse_mode=ParseMode.MARKDOWN
        )
    
    # --- SCHEDULAZIONE ---
    
    @authorized_only
    async def schedule_reminder(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Schedula promemoria - /remind YYYY-MM-DD HH:MM messaggio"""
        if len(context.args) < 3:
            await update.message.reply_text(
                "❌ **Formato non corretto**\n\n"
                "Usa: `/remind <data> <ora> <messaggio>`\n\n"
                "**Esempi:**\n"
                "• `/remind 2025-01-15 09:00 Controllo medico`\n"
                "• `/remind 2025-02-01 18:30 Chiamare mamma`\n"
                "• `/remind 2025-03-10 12:00 Pausa pranzo mindfulness`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        try:
            user_id = update.effective_user.id
            date_str = context.args[0]
            time_str = context.args[1]
            message = ' '.join(context.args[2:])
            
            # Parse datetime
            datetime_str = f"{date_str} {time_str}"
            send_time = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")
            
            # Verifica che la data sia nel futuro
            if send_time <= datetime.now():
                await update.message.reply_text(
                    "❌ **Data non valida**\n\n"
                    "La data deve essere nel futuro!"
                )
                return
            
            # Schedula il messaggio
            message_id = scheduler.schedule_one_time_message(user_id, message, send_time)
            
            if message_id:
                await update.message.reply_text(
                    f"✅ **Promemoria schedulato!**\n\n"
                    f"📅 Data: {send_time.strftime('%d/%m/%Y alle %H:%M')}\n"
                    f"💬 Messaggio: {message}\n"
                    f"🆔 ID: {message_id}",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await update.message.reply_text("❌ Errore nella schedulazione.")
                
        except ValueError:
            await update.message.reply_text(
                "❌ **Formato data/ora non valido**\n\n"
                "Usa il formato: `YYYY-MM-DD HH:MM`\n"
                "Esempio: `2025-01-15 09:30`",
                parse_mode=ParseMode.MARKDOWN
            )
    
    # --- GESTIONE CALLBACK ---
    
    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Gestisce i callback dei bottoni inline"""
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        if data == "help":
            await self.help_command(update, context)
        elif data == "profile":
            await query.edit_message_text(
                "👤 **GESTIONE PROFILO**\n\n"
                "Usa i comandi:\n"
                "• `/info_save <categoria> <chiave> <valore>`\n"
                "• `/info_get [categoria]`",
                parse_mode=ParseMode.MARKDOWN
            )
        elif data == "goals":
            await query.edit_message_text(
                "🎯 **GESTIONE OBIETTIVI**\n\n"
                "Usa i comandi:\n"
                "• `/goal_add <titolo>`\n"
                "• `/goal_list`",
                parse_mode=ParseMode.MARKDOWN
            )
        elif data == "progress":
            await self.analyze_progress(update, context)
        elif data == "chat":
            await query.edit_message_text(
                "💬 **Chat AI attivata!**\n\n"
                "Usa `/chat <messaggio>` per parlare con l'AI.\n\n"
                "Esempio: `/chat Come posso migliorare la mia produttività?`",
                parse_mode=ParseMode.MARKDOWN
            )
    
    # --- COMANDI SISTEMA ---
    
    @authorized_only
    async def status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Mostra stato del sistema"""
        status_text = "⚙️ **STATO SISTEMA**\n\n"
        status_text += f"🤖 Bot: ✅ Attivo\n"
        status_text += f"📊 Database: ✅ Connesso\n"
        status_text += f"⏰ Scheduler: {'✅ Attivo' if scheduler.is_running else '❌ Inattivo'}\n"
        status_text += f"🧠 AI: ✅ Disponibile\n\n"
        
        # Statistiche utente
        user_id = update.effective_user.id
        goals = db.get_user_goals(user_id)
        conversations = db.get_user_conversations(user_id, limit=1)
        
        status_text += f"📈 **LE TUE STATISTICHE**\n"
        status_text += f"🎯 Obiettivi attivi: {len(goals)}\n"
        status_text += f"💬 Conversazioni: {len(conversations)}\n"
        
        await update.message.reply_text(status_text, parse_mode=ParseMode.MARKDOWN)
    
    @authorized_only
    async def list_jobs(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Lista job schedulati"""
        jobs = scheduler.list_jobs()
        
        if not jobs:
            await update.message.reply_text(
                "📭 **Nessun promemoria attivo**\n\n"
                "Usa `/remind` per schedulare promemoria!",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        jobs_text = "⏰ **PROMEMORIA ATTIVI**\n\n"
        
        for job in jobs:
            jobs_text += f"🆔 **{job['id']}**\n"
            jobs_text += f"📝 {job['name']}\n"
            if job['next_run']:
                next_run = datetime.fromisoformat(job['next_run'].replace('Z', '+00:00'))
                jobs_text += f"⏰ Prossima esecuzione: {next_run.strftime('%d/%m/%Y %H:%M')}\n"
            jobs_text += f"🔧 Trigger: {job['trigger']}\n\n"
        
        await update.message.reply_text(jobs_text, parse_mode=ParseMode.MARKDOWN)
    
    # --- INIZIALIZZAZIONE BOT ---
    
    def setup_handlers(self):
        """Configura tutti i gestori di comandi"""
        # Comandi principali
        self.application.add_handler(CommandHandler("start", self.start))
        self.application.add_handler(CommandHandler("help", self.help_command))
        
        # Gestione profilo
        self.application.add_handler(CommandHandler("info_save", self.save_info))
        self.application.add_handler(CommandHandler("info_get", self.get_info))
        
        # Gestione obiettivi
        self.application.add_handler(CommandHandler("goal_add", self.add_goal))
        self.application.add_handler(CommandHandler("goal_list", self.list_goals))
        
        # Conversazione AI
        self.application.add_handler(CommandHandler("chat", self.chat_ai))
        self.application.add_handler(CommandHandler("motivation", self.get_motivation))
        self.application.add_handler(CommandHandler("analyze", self.analyze_progress))
        
        # Schedulazione
        self.application.add_handler(CommandHandler("remind", self.schedule_reminder))
        
        # Sistema
        self.application.add_handler(CommandHandler("status", self.status))
        self.application.add_handler(CommandHandler("jobs", self.list_jobs))
        
        # Callback handlers
        self.application.add_handler(CallbackQueryHandler(self.handle_callback))
        
        logger.info("Handlers configurati con successo")
    
    def run(self):
        """Avvia il bot"""
        try:
            # Valida configurazione
            validate_config()
            
            # Crea applicazione
            self.application = Application.builder().token(TELEGRAM_BOT_TOKEN).build()
            
            # Configura handlers
            self.setup_handlers()
            
            # Configura scheduler
            scheduler.set_bot_application(self.application)
            scheduler.start()
            
            logger.info("🚀 TeleCoach Bot avviato con successo!")
            
            # Avvia il bot
            self.application.run_polling(drop_pending_updates=True)
            
        except Exception as e:
            logger.error(f"❌ Errore avvio bot: {e}")
            raise
        finally:
            # Cleanup
            try:
                if scheduler.is_running:
                    scheduler.stop()
                logger.info("🛑 Cleanup completato")
            except Exception as cleanup_error:
                logger.error(f"Errore durante cleanup: {cleanup_error}")

# --- FUNZIONE MAIN ---

def main():
    """Funzione principale"""
    bot = TeleCoachBot()
    bot.run()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        logger.info("🛑 Bot fermato dall'utente")
    except Exception as e:
        logger.error(f"❌ Errore critico: {e}")
